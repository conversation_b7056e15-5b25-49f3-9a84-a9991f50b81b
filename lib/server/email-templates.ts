/**
 * Email template configuration
 *
 * This file contains the template IDs for emails sent via Brevo.
 * Update these IDs with your actual template IDs from the Brevo platform.
 */

export const EmailTemplates = {
  // Template for squad invitations
  INVITATION: process.env.BREVO_INVITATION_TEMPLATE_ID
    ? Number(process.env.BREVO_INVITATION_TEMPLATE_ID)
    : 166,

  INVITATION_NEW_USER: process.env.BREVO_INVITATION_NEW_USER_TEMPLATE_ID
    ? Number(process.env.BREVO_INVITATION_NEW_USER_TEMPLATE_ID)
    : 169,

  // Template for squad member joined notification
  SQUAD_MEMBER_JOINED: process.env.BREVO_SQUAD_MEMBER_JOINED_TEMPLATE_ID
    ? Number(process.env.BREVO_SQUAD_MEMBER_JOINED_TEMPLATE_ID)
    : 170,

  TRIP_STARTED: process.env.BREVO_TRIP_STARTED_TEMPLATE_ID
    ? Number(process.env.BREVO_TRIP_STARTED_TEMPLATE_ID)
    : 167,

  TRIP_COMPLETED: process.env.BREVO_TRIP_COMPLETED_TEMPLATE_ID
    ? Number(process.env.BREVO_TRIP_COMPLETED_TEMPLATE_ID)
    : 168,

  // Template for welcome emails
  WELCOME: process.env.BREVO_WELCOME_TEMPLATE_ID
    ? Number(process.env.BREVO_WELCOME_TEMPLATE_ID)
    : undefined,

  // Template for password reset
  PASSWORD_RESET: process.env.BREVO_PASSWORD_RESET_TEMPLATE_ID
    ? Number(process.env.BREVO_PASSWORD_RESET_TEMPLATE_ID)
    : undefined,

  // Local Experience Templates
  EXPERIENCE_BOOKING_CONFIRMATION: process.env.BREVO_EXPERIENCE_BOOKING_CONFIRMATION_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_BOOKING_CONFIRMATION_TEMPLATE_ID)
    : undefined,

  EXPERIENCE_HOST_NOTIFICATION: process.env.BREVO_EXPERIENCE_HOST_NOTIFICATION_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_HOST_NOTIFICATION_TEMPLATE_ID)
    : undefined,

  EXPERIENCE_BOOKING_CANCELLED: process.env.BREVO_EXPERIENCE_BOOKING_CANCELLED_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_BOOKING_CANCELLED_TEMPLATE_ID)
    : undefined,

  // Experience Reminder Templates
  EXPERIENCE_REMINDER_USER: process.env.BREVO_EXPERIENCE_REMINDER_USER_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_REMINDER_USER_TEMPLATE_ID)
    : undefined,

  EXPERIENCE_REMINDER_HOST: process.env.BREVO_EXPERIENCE_REMINDER_HOST_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_REMINDER_HOST_TEMPLATE_ID)
    : undefined,

  // Experience Feedback Request Templates
  EXPERIENCE_FEEDBACK_REQUEST_USER: process.env.BREVO_EXPERIENCE_FEEDBACK_REQUEST_USER_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_FEEDBACK_REQUEST_USER_TEMPLATE_ID)
    : undefined,

  EXPERIENCE_FEEDBACK_REQUEST_HOST: process.env.BREVO_EXPERIENCE_FEEDBACK_REQUEST_HOST_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_FEEDBACK_REQUEST_HOST_TEMPLATE_ID)
    : undefined,
}

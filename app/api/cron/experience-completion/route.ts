import { NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"
import { Timestamp } from "firebase-admin/firestore"

// Rate limiting and origin validation for security
const ALLOWED_ORIGINS = [
  "https://vercel.com",
  "https://cron-job.vercel.app",
  process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
].filter(Boolean)

const CRON_SECRET = process.env.CRON_SECRET || "default-secret"

/**
 * Validate cron request authorization and origin
 */
function validateCronRequest(request: NextRequest) {
  // Check authorization header
  const authHeader = request.headers.get("authorization")
  const expectedAuth = `Bearer ${CRON_SECRET}`

  if (!authHeader || authHeader !== expectedAuth) {
    return { isValid: false, error: "Unauthorized" }
  }

  // Check origin for additional security
  const origin = request.headers.get("origin")
  const userAgent = request.headers.get("user-agent")

  // Allow requests from Vercel cron or direct server calls
  if (origin && !ALLOWED_ORIGINS.includes(origin)) {
    // Allow if it's a Vercel cron job (no origin but specific user agent pattern)
    if (!userAgent?.includes("vercel")) {
      return { isValid: false, error: "Forbidden origin" }
    }
  }

  return { isValid: true }
}

/**
 * Process experience completion status updates
 */
async function processExperienceCompletion() {
  const startTime = Date.now()
  const currentTime = new Date()
  
  console.log(`✅ Starting experience completion processing at ${currentTime.toISOString()}`)

  const { adminDb } = getAdminInstance()
  
  // Get all confirmed bookings that haven't been completed yet
  const bookingsQuery = adminDb.collectionGroup("bookings")
    .where("status", "==", "confirmed")
    .where("paymentStatus", "==", "paid")

  const bookingsSnapshot = await bookingsQuery.get()
  
  console.log(`Found ${bookingsSnapshot.size} confirmed bookings to check for completion`)

  const completedBookings: Array<{
    bookingId: string
    experienceId: string
    bookingData: any
  }> = []

  const batch = adminDb.batch()
  let batchCount = 0

  // Process each booking
  for (const bookingDoc of bookingsSnapshot.docs) {
    try {
      const bookingData = bookingDoc.data()
      const bookingId = bookingDoc.id
      
      // Extract experience ID from the document path
      const pathParts = bookingDoc.ref.path.split("/")
      const experienceId = pathParts[1] // localExperiences/{experienceId}/bookings/{bookingId}

      // Parse the experience date and time
      const experienceDate = bookingData.date // YYYY-MM-DD format
      const experienceTime = bookingData.time // HH:MM format
      
      // Create full datetime in UTC
      const experienceDateTime = new Date(`${experienceDate}T${experienceTime}:00.000Z`)
      
      // Get the experience details to determine duration
      let experienceDurationMinutes = 120 // Default 2 hours

      try {
        const experienceDoc = await adminDb.collection("localExperiences").doc(experienceId).get()
        if (experienceDoc.exists) {
          const experienceData = experienceDoc.data()
          experienceDurationMinutes = experienceData?.duration || 120
        }
      } catch (error) {
        console.warn(`Could not fetch experience ${experienceId} for duration, using default`)
      }

      // Add the duration to get the end time (duration is in minutes)
      const experienceEndTime = new Date(experienceDateTime.getTime() + (experienceDurationMinutes * 60 * 1000))
      
      // Check if experience has ended
      if (experienceEndTime <= currentTime) {
        console.log(`Marking booking ${bookingId} as completed (ended: ${experienceEndTime.toISOString()})`)
        
        // Update booking status to completed
        batch.update(bookingDoc.ref, {
          status: "completed",
          completedAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        })

        batchCount++

        // Track for reporting
        completedBookings.push({
          bookingId,
          experienceId,
          bookingData
        })

        // Commit batch if it gets too large
        if (batchCount >= 500) {
          await batch.commit()
          console.log(`Committed batch of ${batchCount} completion updates`)
          batchCount = 0
        }
      }
    } catch (error) {
      console.error(`Error processing booking ${bookingDoc.id}:`, error)
    }
  }

  // Commit remaining updates
  if (batchCount > 0) {
    await batch.commit()
    console.log(`Committed final batch of ${batchCount} completion updates`)
  }

  const duration = Date.now() - startTime

  return {
    bookingsProcessed: bookingsSnapshot.size,
    bookingsCompleted: completedBookings.length,
    completedBookings: completedBookings.map(b => ({
      bookingId: b.bookingId,
      experienceId: b.experienceId,
      experienceTitle: b.bookingData.experienceTitle,
      userName: b.bookingData.userName,
      date: b.bookingData.date,
      time: b.bookingData.time
    })),
    duration
  }
}

export async function GET(request: NextRequest) {
  try {
    const validation = validateCronRequest(request)
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.error === "Unauthorized" ? 401 : 403 }
      )
    }

    const result = await processExperienceCompletion()

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      ...result,
    }

    console.log("Experience completion processing completed:", response)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error in experience completion cron job:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

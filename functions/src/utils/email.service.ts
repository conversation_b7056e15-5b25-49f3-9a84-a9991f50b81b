import { TransactionalEmailsApi, SendSmtpEmail } from "@getbrevo/brevo"
import { EmailTemplates } from "./email-templates"

/**
 * Email service for Firebase Functions
 * Server-side implementation using Brevo API
 * Generic service that matches the main app's email service functionality
 */
export class EmailService {
  private static apiInstance: TransactionalEmailsApi | null = null

  /**
   * Initialize the Brevo API client
   */
  private static initializeBrevoClient(): TransactionalEmailsApi {
    if (!this.apiInstance) {
      const apiKey = process.env.BREVO_API_KEY

      if (!apiKey) {
        throw new Error("BREVO_API_KEY is not configured in Firebase Functions environment")
      }

      this.apiInstance = new TransactionalEmailsApi()
      this.apiInstance.setApiKey(0, apiKey)
    }

    return this.apiInstance
  }

  /**
   * Send an email using the Brevo API
   */
  static async sendEmail(options: {
    to: string
    cc?: string[]
    bcc?: string[]
    subject?: string
    templateId?: number
    params?: Record<string, any>
    htmlContent?: string
    from?: string
  }): Promise<{
    success: boolean
    messageId?: string
    error?: string
  }> {
    try {
      const apiInstance = this.initializeBrevoClient()

      // Create the send email request
      const sendSmtpEmail = new SendSmtpEmail()

      // Set the sender
      sendSmtpEmail.sender = {
        name: "Togeda.ai",
        email: options.from || process.env.EMAIL_FROM,
      }

      // Set the recipient
      sendSmtpEmail.to = [{ email: options.to }]

      // Set CC recipients if provided
      if (options.cc && options.cc.length > 0) {
        sendSmtpEmail.cc = options.cc.map((email) => ({ email }))
      }

      // Set BCC recipients if provided
      if (options.bcc && options.bcc.length > 0) {
        sendSmtpEmail.bcc = options.bcc.map((email) => ({ email }))
      }

      // Set template or content
      if (options.templateId) {
        sendSmtpEmail.templateId = options.templateId
        if (options.params) {
          sendSmtpEmail.params = options.params
        }
      } else {
        sendSmtpEmail.subject = options.subject || "Notification"
        sendSmtpEmail.htmlContent = options.htmlContent || ""
      }

      // Send the email
      const data = await apiInstance.sendTransacEmail(sendSmtpEmail)

      // Extract messageId from response
      const messageId = data?.body?.messageId || `email-${Date.now()}`

      console.log(`Email sent successfully to ${options.to}, messageId: ${messageId}`)

      return {
        success: true,
        messageId,
      }
    } catch (error) {
      console.error("Error sending email via Brevo:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  /**
   * Send booking confirmation email to guest
   */
  static async sendBookingConfirmationEmail(
    guestEmail: string,
    bookingDetails: {
      bookingId: string
      guestName: string
      experienceTitle: string
      hostName: string
      hostEmail?: string
      hostPhone?: string
      bookingDate: string
      bookingTime: string
      guestCount: number
      totalAmount: number
      specialRequests?: string
      experienceLocation: string
      experienceDescription: string
      cancellationPolicy: string
    },
    options?: {
      bcc?: string[]
      cc?: string[]
    }
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const templateId = EmailTemplates.EXPERIENCE_BOOKING_CONFIRMATION

    if (!templateId) {
      console.warn("EXPERIENCE_BOOKING_CONFIRMATION template not configured")
      return { success: false, error: "Email template not configured" }
    }

    return this.sendEmail({
      to: guestEmail,
      bcc: options?.bcc,
      cc: options?.cc,
      templateId,
      params: {
        guestName: bookingDetails.guestName,
        experienceTitle: bookingDetails.experienceTitle,
        bookingId: bookingDetails.bookingId,
        hostName: bookingDetails.hostName,
        hostEmail: bookingDetails.hostEmail || "Not Provided",
        hostPhone: bookingDetails.hostPhone || "Not Provided",
        bookingDate: bookingDetails.bookingDate,
        bookingTime: bookingDetails.bookingTime,
        guestCount: bookingDetails.guestCount,
        totalAmount: bookingDetails.totalAmount,
        specialRequests: bookingDetails.specialRequests || "None",
        experienceLocation: bookingDetails.experienceLocation,
        experienceDescription: bookingDetails.experienceDescription,
        cancellationPolicy: bookingDetails.cancellationPolicy,
        bookingDetailsUrl: `${process.env.APP_BASE_URL}/experiences?tab=my-bookings`,
      },
    })
  }

  /**
   * Send host notification email when a new booking is made
   */
  static async sendHostNotificationEmail(
    hostEmail: string,
    bookingDetails: {
      bookingId: string
      hostName: string
      experienceTitle: string
      guestName: string
      guestEmail: string
      guestPhone?: string
      bookingDate: string
      bookingTime: string
      guestCount: number
      totalAmount: number
      specialRequests?: string
      experienceLocation: string
    }
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const templateId = EmailTemplates.EXPERIENCE_HOST_NOTIFICATION

    if (!templateId) {
      console.warn("EXPERIENCE_HOST_NOTIFICATION template not configured")
      return { success: false, error: "Email template not configured" }
    }

    return this.sendEmail({
      to: hostEmail,
      templateId,
      params: {
        hostName: bookingDetails.hostName,
        experienceTitle: bookingDetails.experienceTitle,
        bookingId: bookingDetails.bookingId,
        guestName: bookingDetails.guestName,
        guestEmail: bookingDetails.guestEmail,
        guestPhone: bookingDetails.guestPhone || "Not Provided",
        bookingDate: bookingDetails.bookingDate,
        bookingTime: bookingDetails.bookingTime,
        guestCount: bookingDetails.guestCount,
        totalAmount: bookingDetails.totalAmount,
        specialRequests: bookingDetails.specialRequests || "None",
        experienceLocation: bookingDetails.experienceLocation,
        hostDashboardUrl: `${process.env.APP_BASE_URL}/experiences`,
      },
    })
  }

  /**
   * Send reminder email to user or host
   */
  static async sendReminderEmail(
    email: string,
    reminderDetails: {
      bookingId: string
      guestName: string
      experienceTitle: string
      hostName: string
      bookingDate: string
      bookingTime: string
      experienceLocation: string
      reminderType: "7d" | "3d" | "1d" | "2h"
      guestCount: number
    },
    recipientType: "user" | "host"
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const templateId =
      recipientType === "user"
        ? EmailTemplates.EXPERIENCE_REMINDER_USER
        : EmailTemplates.EXPERIENCE_REMINDER_HOST

    if (!templateId) {
      console.warn(`EXPERIENCE_REMINDER_${recipientType.toUpperCase()} template not configured`)
      return { success: false, error: "Email template not configured" }
    }

    // Format reminder type for display
    const reminderTypeDisplay = {
      "7d": "7 days",
      "3d": "3 days",
      "1d": "1 day",
      "2h": "2 hours",
    }[reminderDetails.reminderType]

    return this.sendEmail({
      to: email,
      templateId,
      params: {
        recipientName:
          recipientType === "user" ? reminderDetails.guestName : reminderDetails.hostName,
        experienceTitle: reminderDetails.experienceTitle,
        bookingId: reminderDetails.bookingId,
        guestName: reminderDetails.guestName,
        hostName: reminderDetails.hostName,
        bookingDate: reminderDetails.bookingDate,
        bookingTime: reminderDetails.bookingTime,
        experienceLocation: reminderDetails.experienceLocation,
        guestCount: reminderDetails.guestCount,
        reminderType: reminderDetails.reminderType,
        reminderTypeDisplay,
        experienceDetailsUrl: `${process.env.APP_BASE_URL}/experiences?tab=my-bookings`,
      },
    })
  }

  /**
   * Send feedback request email to user or host
   */
  static async sendFeedbackRequestEmail(
    email: string,
    feedbackDetails: {
      bookingId: string
      experienceId: string
      guestName: string
      experienceTitle: string
      hostName: string
      bookingDate: string
      bookingTime: string
      experienceLocation: string
      userFeedbackUrl: string
      hostFeedbackUrl: string
    },
    recipientType: "user" | "host"
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const templateId =
      recipientType === "user"
        ? EmailTemplates.EXPERIENCE_FEEDBACK_REQUEST_USER
        : EmailTemplates.EXPERIENCE_FEEDBACK_REQUEST_HOST

    if (!templateId) {
      console.warn(
        `EXPERIENCE_FEEDBACK_REQUEST_${recipientType.toUpperCase()} template not configured`
      )
      return { success: false, error: "Email template not configured" }
    }

    const feedbackUrl =
      recipientType === "user" ? feedbackDetails.userFeedbackUrl : feedbackDetails.hostFeedbackUrl

    return this.sendEmail({
      to: email,
      templateId,
      params: {
        recipientName:
          recipientType === "user" ? feedbackDetails.guestName : feedbackDetails.hostName,
        experienceTitle: feedbackDetails.experienceTitle,
        bookingId: feedbackDetails.bookingId,
        experienceId: feedbackDetails.experienceId,
        guestName: feedbackDetails.guestName,
        hostName: feedbackDetails.hostName,
        bookingDate: feedbackDetails.bookingDate,
        bookingTime: feedbackDetails.bookingTime,
        experienceLocation: feedbackDetails.experienceLocation,
        feedbackUrl,
      },
    })
  }
}
